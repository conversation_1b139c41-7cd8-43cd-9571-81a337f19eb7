import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { Journey, CreateJourney, UpdateJourney } from '../models/journey.model';

@Injectable({
  providedIn: 'root'
})
export class JourneyService {
  private readonly apiUrl = 'https://localhost:7115/api/journeys';

  constructor(private http: HttpClient) { }

  getAllJourneys(): Observable<Journey[]> {
    return this.http.get<Journey[]>(this.apiUrl)
      .pipe(catchError(this.handleError));
  }

  getJourneyById(id: number): Observable<Journey> {
    return this.http.get<Journey>(`${this.apiUrl}/${id}`)
      .pipe(catchError(this.handleError));
  }

  createJourney(journey: CreateJourney): Observable<Journey> {
    return this.http.post<Journey>(this.apiUrl, journey)
      .pipe(catchError(this.handleError));
  }

  updateJourney(id: number, journey: UpdateJourney): Observable<Journey> {
    return this.http.put<Journey>(`${this.apiUrl}/${id}`, journey)
      .pipe(catchError(this.handleError));
  }

  deleteJourney(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`)
      .pipe(catchError(this.handleError));
  }

  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred!';
    
    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side error
      if (error.status === 400 && error.error) {
        errorMessage = error.error;
      } else if (error.status === 404) {
        errorMessage = 'Journey not found.';
      } else if (error.status === 500) {
        errorMessage = 'Internal server error. Please try again later.';
      } else {
        errorMessage = `Error Code: ${error.status}\nMessage: ${error.message}`;
      }
    }
    
    return throwError(() => new Error(errorMessage));
  }
}
