<div class="journey-list-container">
  <div class="header-section">
    <h2>Journey Management</h2>
    <button class="btn btn-primary" routerLink="/journeys/new">
      <span class="icon">+</span> Add New Journey
    </button>
  </div>

  <div class="search-section">
    <div class="search-box">
      <input
        type="text"
        placeholder="Search journeys by code or description..."
        [(ngModel)]="searchTerm"
        (input)="onSearch()"
        class="search-input">
      <span class="search-icon">🔍</span>
    </div>
  </div>

  <div class="table-container" *ngIf="!loading">
    <table class="journeys-table" *ngIf="filteredJourneys.length > 0; else noJourneys">
      <thead>
        <tr>
          <th (click)="sort('code')" class="sortable">
            Code
            <span class="sort-indicator" [class.active]="sortField === 'code'">
              {{ getSortIcon('code') }}
            </span>
          </th>
          <th (click)="sort('description')" class="sortable">
            Description
            <span class="sort-indicator" [class.active]="sortField === 'description'">
              {{ getSortIcon('description') }}
            </span>
          </th>
          <th>Stops</th>
          <th>Route</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let journey of filteredJourneys">
          <td class="code-cell" data-label="Code">{{ journey.code }}</td>
          <td class="description-cell" data-label="Description">{{ journey.description }}</td>
          <td class="stops-cell" data-label="Stops">
            <span class="stops-count">{{ journey.stops.length }} stops</span>
          </td>
          <td class="route-cell" data-label="Route">
            <div class="route-preview" *ngIf="journey.stops.length > 0">
              <span class="route-start">{{ journey.stops[0].stop.code }}</span>
              <span class="route-arrow" *ngIf="journey.stops.length > 1">→</span>
              <span class="route-end" *ngIf="journey.stops.length > 1">{{ journey.stops[journey.stops.length - 1].stop.code }}</span>
              <span class="route-via" *ngIf="journey.stops.length > 2">
                (via {{ journey.stops.length - 2 }} stops)
              </span>
            </div>
            <span class="no-route" *ngIf="journey.stops.length === 0">No route defined</span>
          </td>
          <td class="actions-cell" data-label="Actions">
            <button class="btn btn-sm btn-info" [routerLink]="['/journeys', journey.id]" title="View Details">
              👁️
            </button>
            <button class="btn btn-sm btn-warning" [routerLink]="['/journeys', journey.id, 'edit']" title="Edit">
              ✏️
            </button>
            <button class="btn btn-sm btn-danger" (click)="confirmDelete(journey)" title="Delete">
              🗑️
            </button>
          </td>
        </tr>
      </tbody>
    </table>

    <ng-template #noJourneys>
      <div class="no-data">
        <div class="no-data-icon">🚌</div>
        <h3>No journeys found</h3>
        <p *ngIf="searchTerm">No journeys match your search criteria.</p>
        <p *ngIf="!searchTerm">No journeys have been created yet.</p>
        <button class="btn btn-primary" routerLink="/journeys/new">Create First Journey</button>
      </div>
    </ng-template>
  </div>

  <div class="loading-section" *ngIf="loading">
    <div class="loading-spinner">
      <div class="spinner"></div>
      <p>Loading journeys...</p>
    </div>
  </div>

  <div class="error-section" *ngIf="error">
    <div class="error-message">
      <span class="error-icon">⚠️</span>
      <p>{{ error }}</p>
      <button class="btn btn-primary" (click)="loadJourneys()">Retry</button>
    </div>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal-overlay" *ngIf="showDeleteModal" (click)="cancelDelete()">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <h3>Confirm Delete</h3>
    <p *ngIf="journeyToDelete">
      Are you sure you want to delete journey <strong>{{ journeyToDelete.code }}</strong>?
    </p>
    <p class="warning-text">This action cannot be undone.</p>
    <div class="modal-actions">
      <button class="btn btn-secondary" (click)="cancelDelete()">Cancel</button>
      <button class="btn btn-danger" (click)="deleteJourney()" [disabled]="deleting">
        {{ deleting ? 'Deleting...' : 'Delete' }}
      </button>
    </div>
  </div>
</div>
