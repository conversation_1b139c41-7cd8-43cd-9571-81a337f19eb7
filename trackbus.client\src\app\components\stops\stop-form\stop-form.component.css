.stop-form-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.header-section h2 {
  color: #1976d2;
  margin: 0;
}

.form-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.stop-form {
  max-width: 600px;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #333;
}

.form-control {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.3s;
}

.form-control:focus {
  outline: none;
  border-color: #1976d2;
}

.form-control.error {
  border-color: #dc3545;
}

.coordinates-section {
  margin: 2rem 0;
  padding: 1.5rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.coordinates-section h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: #1976d2;
}

.coordinates-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.coordinates-help {
  margin-top: 1rem;
  color: #666;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #eee;
}

.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s;
  min-width: 120px;
}

.btn-primary {
  background-color: #1976d2;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #1565c0;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #5a6268;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.error-message {
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.error-section {
  margin-top: 1.5rem;
}

.error-section .error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 4px;
  border: 1px solid #f5c6cb;
  display: flex;
  align-items: center;
}

.error-icon {
  font-size: 1.25rem;
  margin-right: 0.5rem;
}

/* Enhanced Responsive Design */

/* Tablet styles */
@media (max-width: 1024px) {
  .stop-form-container {
    max-width: 100%;
    padding: 1.5rem;
  }

  .coordinates-grid {
    gap: 1.5rem;
  }

  .btn {
    min-width: 140px;
  }
}

/* Mobile styles */
@media (max-width: 768px) {
  .stop-form-container {
    padding: 1rem;
  }

  .header-section {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .header-section h2 {
    font-size: 1.5rem;
    text-align: center;
  }

  .form-section {
    padding: 1.5rem;
    border-radius: 0;
    margin: 0 -1rem;
  }

  .form-group {
    margin-bottom: 1.25rem;
  }

  .form-control {
    padding: 1rem;
    font-size: 1.1rem;
    border-radius: 8px;
    min-height: 44px; /* Touch-friendly minimum */
  }

  .coordinates-grid {
    grid-template-columns: 1fr;
    gap: 1.25rem;
  }

  .form-actions {
    flex-direction: column;
    gap: 1rem;
    margin-top: 2.5rem;
    padding-top: 2rem;
  }

  .btn {
    width: 100%;
    min-height: 48px;
    font-size: 1.1rem;
    padding: 1rem 1.5rem;
    border-radius: 8px;
  }

  .error-message {
    font-size: 1rem;
    margin-top: 0.5rem;
  }

  .error-section .error-message {
    padding: 1.25rem;
    border-radius: 8px;
    font-size: 1rem;
  }

  .error-icon {
    font-size: 1.5rem;
    margin-right: 0.75rem;
  }
}

/* Small mobile styles */
@media (max-width: 480px) {
  .stop-form-container {
    padding: 0.75rem;
  }

  .form-section {
    padding: 1rem;
  }

  .header-section h2 {
    font-size: 1.25rem;
  }

  .form-control {
    font-size: 1rem;
    padding: 0.875rem;
  }

  .btn {
    font-size: 1rem;
    padding: 0.875rem 1.25rem;
    min-height: 44px;
  }
}