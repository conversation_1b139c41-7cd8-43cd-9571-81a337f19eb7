{"Version": 1, "Hash": "1NjjjoyeREJnOEuuWjcEzXSr1Yp9lxNhk9dw8MSgpaY=", "Source": "TrackBus.Server", "BasePath": "_content/TrackBus.Server", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [{"Identity": "C:\\Users\\<USER>\\Desktop\\Projects\\Random Projects\\mahmuti-adrijan\\trackbus.client\\trackbus.client.esproj", "Version": 2, "Source": "trackbus.client", "GetPublishAssetsTargets": "GetCurrentProjectPublishStaticWebAssetItems", "AdditionalPublishProperties": "Configuration=Debug;Platform=AnyCPU", "AdditionalPublishPropertiesToRemove": "WebPublishProfileFile;TargetFramework", "GetBuildAssetsTargets": "GetCurrentProjectBuildStaticWebAssetItems", "AdditionalBuildProperties": "Configuration=Debug;Platform=AnyCPU", "AdditionalBuildPropertiesToRemove": "WebPublishProfileFile;TargetFramework"}], "DiscoveryPatterns": [], "Assets": [], "Endpoints": []}