<div class="stop-list-container">
  <div class="header-section">
    <h2>Stop Management</h2>
    <button class="btn btn-primary" routerLink="/stops/new">
      <span class="icon">+</span> Add New Stop
    </button>
  </div>

  <div class="search-section">
    <div class="search-box">
      <input
        type="text"
        placeholder="Search stops by code or description..."
        [(ngModel)]="searchTerm"
        (input)="onSearch()"
        class="search-input">
      <span class="search-icon">🔍</span>
    </div>
  </div>

  <div class="table-container" *ngIf="!loading">
    <table class="stops-table" *ngIf="filteredStops.length > 0; else noStops">
      <thead>
        <tr>
          <th (click)="sort('code')" class="sortable">
            Code
            <span class="sort-indicator" [class.active]="sortField === 'code'">
              {{ getSortIcon('code') }}
            </span>
          </th>
          <th (click)="sort('description')" class="sortable">
            Description
            <span class="sort-indicator" [class.active]="sortField === 'description'">
              {{ getSortIcon('description') }}
            </span>
          </th>
          <th>Coordinates</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let stop of filteredStops">
          <td class="code-cell" data-label="Code">{{ stop.code }}</td>
          <td class="description-cell" data-label="Description">{{ stop.description }}</td>
          <td class="coordinates-cell" data-label="Coordinates">{{ stop.x }}, {{ stop.y }}</td>
          <td class="actions-cell" data-label="Actions">
            <button class="btn btn-sm btn-info" [routerLink]="['/stops', stop.id]" title="View Details">
              👁️
            </button>
            <button class="btn btn-sm btn-warning" [routerLink]="['/stops', stop.id, 'edit']" title="Edit">
              ✏️
            </button>
            <button class="btn btn-sm btn-danger" (click)="confirmDelete(stop)" title="Delete">
              🗑️
            </button>
          </td>
        </tr>
      </tbody>
    </table>

    <ng-template #noStops>
      <div class="no-data">
        <div class="no-data-icon">🚏</div>
        <h3>No stops found</h3>
        <p *ngIf="searchTerm">No stops match your search criteria.</p>
        <p *ngIf="!searchTerm">No stops have been created yet.</p>
        <button class="btn btn-primary" routerLink="/stops/new">Create First Stop</button>
      </div>
    </ng-template>
  </div>

  <div class="loading-section" *ngIf="loading">
    <div class="loading-spinner">
      <div class="spinner"></div>
      <p>Loading stops...</p>
    </div>
  </div>

  <div class="error-section" *ngIf="error">
    <div class="error-message">
      <span class="error-icon">⚠️</span>
      <p>{{ error }}</p>
      <button class="btn btn-primary" (click)="loadStops()">Retry</button>
    </div>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal-overlay" *ngIf="showDeleteModal" (click)="cancelDelete()">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <h3>Confirm Delete</h3>
    <p *ngIf="stopToDelete">
      Are you sure you want to delete stop <strong>{{ stopToDelete.code }}</strong>?
    </p>
    <p class="warning-text">This action cannot be undone.</p>
    <div class="modal-actions">
      <button class="btn btn-secondary" (click)="cancelDelete()">Cancel</button>
      <button class="btn btn-danger" (click)="deleteStop()" [disabled]="deleting">
        {{ deleting ? 'Deleting...' : 'Delete' }}
      </button>
    </div>
  </div>
</div>
