<div class="app-container">
  <header class="app-header">
    <nav class="navbar">
      <div class="navbar-brand">
        <h1>TrackBus - TUG Journey Management</h1>
      </div>
      <div class="navbar-nav">
        <a routerLink="/stops" routerLinkActive="active" class="nav-link">Stops</a>
        <a routerLink="/journeys" routerLinkActive="active" class="nav-link">Journeys</a>
      </div>
    </nav>
  </header>

  <main class="app-main">
    <div class="container">
      <router-outlet></router-outlet>
    </div>
  </main>

  <div *ngIf="loading$ | async" class="loading-overlay">
    <div class="loading-spinner">
      <div class="spinner"></div>
      <p>Loading...</p>
    </div>
  </div>
</div>
