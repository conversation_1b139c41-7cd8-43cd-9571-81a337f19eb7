import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { Stop, CreateStop, UpdateStop } from '../models/stop.model';

@Injectable({
  providedIn: 'root'
})
export class StopService {
  private readonly apiUrl = 'https://localhost:7115/api/stops';

  constructor(private http: HttpClient) { }

  getAllStops(): Observable<Stop[]> {
    return this.http.get<Stop[]>(this.apiUrl)
      .pipe(catchError(this.handleError));
  }

  getStopById(id: number): Observable<Stop> {
    return this.http.get<Stop>(`${this.apiUrl}/${id}`)
      .pipe(catchError(this.handleError));
  }

  createStop(stop: CreateStop): Observable<Stop> {
    return this.http.post<Stop>(this.apiUrl, stop)
      .pipe(catchError(this.handleError));
  }

  updateStop(id: number, stop: UpdateStop): Observable<Stop> {
    return this.http.put<Stop>(`${this.apiUrl}/${id}`, stop)
      .pipe(catchError(this.handleError));
  }

  deleteStop(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`)
      .pipe(catchError(this.handleError));
  }

  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred!';
    
    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side error
      if (error.status === 400 && error.error) {
        errorMessage = error.error;
      } else if (error.status === 404) {
        errorMessage = 'Stop not found.';
      } else if (error.status === 500) {
        errorMessage = 'Internal server error. Please try again later.';
      } else {
        errorMessage = `Error Code: ${error.status}\nMessage: ${error.message}`;
      }
    }
    
    return throwError(() => new Error(errorMessage));
  }
}
