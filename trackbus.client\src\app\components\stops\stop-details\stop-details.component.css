.stop-details-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.header-section h2 {
  color: #1976d2;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.details-section {
  margin-bottom: 2rem;
}

.details-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  background: linear-gradient(135deg, #1976d2, #1565c0);
  color: white;
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 1.5rem;
}

.stop-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.card-content {
  padding: 2rem;
}

.detail-group {
  margin-bottom: 1.5rem;
}

.detail-label {
  display: block;
  font-weight: 600;
  color: #666;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-value {
  font-size: 1.1rem;
  color: #333;
  padding: 0.5rem 0;
}

.code-value {
  font-weight: 600;
  color: #1976d2;
  font-size: 1.25rem;
}

.coordinates-section {
  margin-top: 2rem;
  padding: 1.5rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.coordinates-section h4 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: #1976d2;
}

.coordinates-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.coordinate-item {
  background: white;
  padding: 1rem;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.coordinate-value {
  font-family: monospace;
  font-size: 1.1rem;
  color: #1976d2;
  font-weight: 600;
}

.coordinates-display {
  text-align: center;
  padding: 1rem;
  background: white;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.coordinates-label {
  font-weight: 600;
  color: #666;
  margin-right: 0.5rem;
}

.coordinates-value {
  font-family: monospace;
  font-size: 1.1rem;
  color: #1976d2;
  font-weight: 600;
}

.card-actions {
  padding: 1.5rem 2rem;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s;
}

.btn-primary {
  background-color: #1976d2;
  color: white;
}

.btn-primary:hover {
  background-color: #1565c0;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #5a6268;
}

.btn-warning {
  background-color: #ffc107;
  color: #212529;
}

.btn-warning:hover {
  background-color: #e0a800;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover {
  background-color: #c82333;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.loading-section {
  text-align: center;
  padding: 4rem 2rem;
}

.loading-spinner {
  display: inline-block;
}

.spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1976d2;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-section {
  text-align: center;
  padding: 2rem;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #f5c6cb;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.error-icon {
  font-size: 1.5rem;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modal-content h3 {
  margin-top: 0;
  color: #333;
}

.warning-text {
  color: #dc3545;
  font-size: 0.875rem;
  margin-bottom: 1.5rem;
}

.modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

/* Enhanced Responsive Design */

/* Tablet styles */
@media (max-width: 1024px) {
  .stop-details-container {
    max-width: 100%;
    padding: 1.5rem;
  }

  .coordinates-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .btn {
    min-width: 140px;
  }
}

/* Mobile styles */
@media (max-width: 768px) {
  .stop-details-container {
    padding: 1rem;
  }

  .header-section {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .header-section h2 {
    text-align: center;
    font-size: 1.5rem;
  }

  .header-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .header-actions .btn {
    width: 100%;
    min-height: 48px;
    font-size: 1.1rem;
    border-radius: 8px;
  }

  .details-card {
    border-radius: 12px;
  }

  .card-header {
    padding: 1.25rem;
    flex-direction: column;
    gap: 0.75rem;
    align-items: flex-start;
    text-align: center;
  }

  .card-header h3 {
    font-size: 1.25rem;
    width: 100%;
  }

  .stop-badge {
    align-self: center;
  }

  .card-content {
    padding: 1.5rem;
  }

  .detail-group {
    margin-bottom: 1.25rem;
  }

  .detail-value {
    font-size: 1rem;
  }

  .code-value {
    font-size: 1.1rem;
  }

  .coordinates-section {
    padding: 1.25rem;
    border-radius: 12px;
  }

  .coordinates-grid {
    grid-template-columns: 1fr;
    gap: 1.25rem;
  }

  .coordinate-item {
    padding: 1rem;
    border-radius: 8px;
  }

  .card-actions {
    flex-direction: column;
    gap: 0.75rem;
    padding: 1.25rem;
  }

  .card-actions .btn {
    width: 100%;
    min-height: 48px;
    font-size: 1.1rem;
    border-radius: 8px;
  }

  .modal-content {
    margin: 1rem;
    border-radius: 12px;
    padding: 1.5rem;
  }

  .modal-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .modal-actions .btn {
    min-height: 48px;
    font-size: 1.1rem;
    border-radius: 8px;
  }
}

/* Small mobile styles */
@media (max-width: 480px) {
  .stop-details-container {
    padding: 0.75rem;
  }

  .header-section h2 {
    font-size: 1.25rem;
  }

  .card-header h3 {
    font-size: 1.1rem;
  }

  .card-content {
    padding: 1rem;
  }

  .coordinates-section {
    padding: 1rem;
  }

  .coordinate-item {
    padding: 0.75rem;
  }

  .card-actions {
    padding: 1rem;
  }

  .modal-content {
    margin: 0.75rem;
    padding: 1.25rem;
  }
}