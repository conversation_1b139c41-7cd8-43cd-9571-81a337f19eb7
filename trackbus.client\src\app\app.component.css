﻿.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background-color: #1976d2;
  color: white;
  padding: 0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.navbar-brand h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 500;
}

.navbar-nav {
  display: flex;
  gap: 2rem;
}

.nav-link {
  color: white;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.nav-link.active {
  background-color: rgba(255, 255, 255, 0.2);
  font-weight: 500;
}

.app-main {
  flex: 1;
  padding: 2rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-spinner {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1976d2;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Enhanced Responsive Design */

/* Tablet styles */
@media (max-width: 1024px) {
  .navbar {
    padding: 1rem 1.5rem;
  }

  .container {
    padding: 0 1.5rem;
  }
}

/* Mobile styles */
@media (max-width: 768px) {
  .navbar {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
    text-align: center;
  }

  .navbar-brand h1 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
  }

  .navbar-nav {
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
  }

  .nav-link {
    padding: 0.75rem 1.25rem;
    border-radius: 8px;
    font-size: 1rem;
    min-width: 100px;
    text-align: center;
    min-height: 44px; /* Touch-friendly minimum */
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .app-main {
    padding: 1rem 0;
  }

  .container {
    padding: 0 1rem;
  }

  .loading-overlay {
    padding: 1rem;
  }

  .loading-spinner {
    padding: 1.5rem;
    border-radius: 12px;
  }
}

/* Small mobile styles */
@media (max-width: 480px) {
  .navbar {
    padding: 0.75rem;
  }

  .navbar-brand h1 {
    font-size: 1.1rem;
  }

  .navbar-nav {
    gap: 0.75rem;
  }

  .nav-link {
    padding: 0.625rem 1rem;
    font-size: 0.9rem;
    min-width: 80px;
    min-height: 40px;
  }

  .container {
    padding: 0 0.75rem;
  }

  .loading-spinner {
    padding: 1.25rem;
  }
}
