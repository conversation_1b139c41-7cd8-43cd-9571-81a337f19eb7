.journey-details-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.header-section h2 {
  color: #1976d2;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.details-section {
  margin-bottom: 2rem;
}

.details-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  background: linear-gradient(135deg, #1976d2, #1565c0);
  color: white;
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 1.5rem;
}

.journey-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.card-content {
  padding: 2rem;
}

.basic-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid #eee;
}

.detail-group {
  margin-bottom: 1rem;
}

.detail-label {
  display: block;
  font-weight: 600;
  color: #666;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-value {
  font-size: 1.1rem;
  color: #333;
  padding: 0.5rem 0;
}

.code-value {
  font-weight: 600;
  color: #1976d2;
  font-size: 1.25rem;
}

.stops-count {
  background-color: #e3f2fd;
  color: #1976d2;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 1rem;
  font-weight: 500;
}

.route-section {
  margin-top: 2rem;
}

.route-section h4 {
  color: #1976d2;
  margin-bottom: 1.5rem;
}

.route-overview {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid #e9ecef;
}

.route-summary {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

.route-start,
.route-end {
  background-color: #1976d2;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 600;
  font-size: 1rem;
}

.route-arrow {
  color: #666;
  font-weight: bold;
  font-size: 1.5rem;
}

.route-via {
  color: #666;
  font-style: italic;
}

.stops-timeline {
  position: relative;
}

.timeline-item {
  display: flex;
  margin-bottom: 2rem;
}

.timeline-marker {
  position: relative;
  margin-right: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.marker-circle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #1976d2;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.1rem;
  z-index: 2;
  position: relative;
}

.marker-circle.first {
  background-color: #4caf50;
}

.marker-circle.last {
  background-color: #f44336;
}

.timeline-line {
  width: 3px;
  height: 60px;
  background-color: #ddd;
  margin-top: 0.5rem;
}

.timeline-content {
  flex: 1;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stop-info {
  width: 100%;
}

.stop-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.stop-code {
  color: #1976d2;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.stop-order {
  background-color: #e3f2fd;
  color: #1976d2;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.stop-description {
  color: #333;
  margin-bottom: 1rem;
  font-size: 1rem;
}

.stop-coordinates,
.passing-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.coordinates-label,
.time-label {
  font-weight: 600;
  color: #666;
  font-size: 0.875rem;
}

.coordinates-value {
  font-family: monospace;
  color: #1976d2;
  font-weight: 600;
}

.time-value {
  font-family: monospace;
  color: #4caf50;
  font-weight: 600;
  background-color: #e8f5e8;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.no-route {
  text-align: center;
  padding: 4rem 2rem;
  color: #666;
}

.no-route-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.no-route h4 {
  color: #333;
  margin-bottom: 1rem;
}

.card-actions {
  padding: 1.5rem 2rem;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s;
}

.btn-primary {
  background-color: #1976d2;
  color: white;
}

.btn-primary:hover {
  background-color: #1565c0;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #5a6268;
}

.btn-warning {
  background-color: #ffc107;
  color: #212529;
}

.btn-warning:hover {
  background-color: #e0a800;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover {
  background-color: #c82333;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.loading-section {
  text-align: center;
  padding: 4rem 2rem;
}

.loading-spinner {
  display: inline-block;
}

.spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1976d2;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-section {
  text-align: center;
  padding: 2rem;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #f5c6cb;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.error-icon {
  font-size: 1.5rem;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modal-content h3 {
  margin-top: 0;
  color: #333;
}

.warning-text {
  color: #dc3545;
  font-size: 0.875rem;
  margin-bottom: 1.5rem;
}

.modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

/* Enhanced Responsive Design */

/* Tablet styles */
@media (max-width: 1024px) {
  .journey-details-container {
    max-width: 100%;
    padding: 1.5rem;
  }

  .basic-info {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .btn {
    min-width: 140px;
  }
}

/* Mobile styles */
@media (max-width: 768px) {
  .journey-details-container {
    padding: 1rem;
  }

  .header-section {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .header-section h2 {
    text-align: center;
    font-size: 1.5rem;
  }

  .header-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .header-actions .btn {
    width: 100%;
    min-height: 48px;
    font-size: 1.1rem;
    border-radius: 8px;
  }

  .details-card {
    border-radius: 12px;
  }

  .card-header {
    padding: 1.25rem;
    flex-direction: column;
    gap: 0.75rem;
    align-items: flex-start;
    text-align: center;
  }

  .card-header h3 {
    font-size: 1.25rem;
    width: 100%;
  }

  .journey-badge {
    align-self: center;
  }

  .card-content {
    padding: 1.5rem;
  }

  .basic-info {
    grid-template-columns: 1fr;
    gap: 1.25rem;
  }

  .detail-group {
    margin-bottom: 1.25rem;
  }

  .detail-value {
    font-size: 1rem;
  }

  .code-value {
    font-size: 1.1rem;
  }

  .route-overview {
    padding: 1.25rem;
    border-radius: 12px;
  }

  .route-summary {
    flex-direction: column;
    gap: 0.75rem;
    text-align: center;
  }

  .route-start,
  .route-end {
    padding: 0.75rem 1.25rem;
    font-size: 1rem;
    border-radius: 8px;
  }

  .route-arrow {
    font-size: 1.25rem;
    transform: rotate(90deg);
  }

  .timeline-item {
    flex-direction: column;
    align-items: stretch;
    padding: 1.25rem;
    border-radius: 12px;
    margin-bottom: 1rem;
  }

  .timeline-marker {
    flex-direction: row;
    align-items: center;
    margin-right: 0;
    margin-bottom: 1rem;
    justify-content: flex-start;
  }

  .timeline-number {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }

  .timeline-line {
    width: 60px;
    height: 3px;
    margin-top: 0;
    margin-left: 1rem;
  }

  .timeline-content {
    padding: 1.25rem;
    border-radius: 12px;
  }

  .stop-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .stop-code {
    font-size: 1.1rem;
  }

  .stop-order {
    align-self: flex-start;
  }

  .card-actions {
    flex-direction: column;
    gap: 0.75rem;
    padding: 1.25rem;
  }

  .card-actions .btn {
    width: 100%;
    min-height: 48px;
    font-size: 1.1rem;
    border-radius: 8px;
  }

  .no-route {
    padding: 2rem 1.5rem;
  }

  .no-route-icon {
    font-size: 3rem;
  }

  .no-route h4 {
    font-size: 1.25rem;
  }

  .modal-content {
    margin: 1rem;
    border-radius: 12px;
    padding: 1.5rem;
  }

  .modal-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .modal-actions .btn {
    min-height: 48px;
    font-size: 1.1rem;
    border-radius: 8px;
  }
}

/* Small mobile styles */
@media (max-width: 480px) {
  .journey-details-container {
    padding: 0.75rem;
  }

  .header-section h2 {
    font-size: 1.25rem;
  }

  .card-header h3 {
    font-size: 1.1rem;
  }

  .card-content {
    padding: 1rem;
  }

  .route-overview {
    padding: 1rem;
  }

  .timeline-item {
    padding: 1rem;
  }

  .timeline-content {
    padding: 1rem;
  }

  .card-actions {
    padding: 1rem;
  }

  .modal-content {
    margin: 0.75rem;
    padding: 1.25rem;
  }
}