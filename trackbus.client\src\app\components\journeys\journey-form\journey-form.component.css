.journey-form-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.header-section h2 {
  color: #1976d2;
  margin: 0;
}

.form-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.journey-form {
  max-width: 800px;
}

.basic-info-section {
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid #eee;
}

.basic-info-section h3 {
  color: #1976d2;
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #333;
}

.form-control {
  padding: 0.75rem;
  border: 2px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.3s;
}

.form-control:focus {
  outline: none;
  border-color: #1976d2;
}

.form-control.error {
  border-color: #dc3545;
}

.stops-section {
  margin-bottom: 2rem;
}

.stops-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.stops-header h3 {
  color: #1976d2;
  margin: 0;
}

.stops-help {
  background-color: #e3f2fd;
  border: 1px solid #bbdefb;
  border-radius: 4px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  color: #1976d2;
}

.stops-list {
  margin-bottom: 1.5rem;
}

.stop-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.stop-order {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  background-color: #1976d2;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-top: 1.5rem;
}

.order-number {
  font-size: 1.1rem;
}

.stop-details {
  flex: 1;
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1rem;
}

.stop-actions {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 1.5rem;
}

.no-stops-available,
.no-stops-exist {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  padding: 1rem;
  color: #856404;
  text-align: center;
}

.no-stops-exist a {
  color: #1976d2;
  text-decoration: none;
}

.no-stops-exist a:hover {
  text-decoration: underline;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #eee;
}

.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s;
  min-width: 120px;
}

.btn-sm {
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  min-width: auto;
}

.btn-primary {
  background-color: #1976d2;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #1565c0;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #5a6268;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #c82333;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.error-message {
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.error-section {
  margin-top: 1.5rem;
}

.error-section .error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 4px;
  border: 1px solid #f5c6cb;
  display: flex;
  align-items: center;
}

.error-icon {
  font-size: 1.25rem;
  margin-right: 0.5rem;
}

/* Enhanced Responsive Design */

/* Tablet styles */
@media (max-width: 1024px) {
  .journey-form-container {
    max-width: 100%;
    padding: 1.5rem;
  }

  .stop-details {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .btn {
    min-width: 140px;
  }
}

/* Mobile styles */
@media (max-width: 768px) {
  .journey-form-container {
    padding: 1rem;
  }

  .header-section {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .header-section h2 {
    font-size: 1.5rem;
    text-align: center;
  }

  .form-section {
    padding: 1.5rem;
    border-radius: 0;
    margin: 0 -1rem;
  }

  .basic-info-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
  }

  .form-group {
    margin-bottom: 1.25rem;
  }

  .form-control {
    padding: 1rem;
    font-size: 1.1rem;
    border-radius: 8px;
    min-height: 44px; /* Touch-friendly minimum */
  }

  .stops-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .stops-header h3 {
    text-align: center;
    font-size: 1.25rem;
  }

  .stop-item {
    flex-direction: column;
    gap: 1.5rem;
    padding: 1.25rem;
    border-radius: 12px;
  }

  .stop-order {
    align-self: center;
    margin-top: 0;
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }

  .stop-details {
    grid-template-columns: 1fr;
    gap: 1.25rem;
  }

  .stop-actions {
    flex-direction: row;
    justify-content: center;
    gap: 0.75rem;
    margin-top: 0;
  }

  .stop-actions .btn {
    min-width: 44px;
    min-height: 44px;
    padding: 0.75rem;
    font-size: 1.1rem;
    border-radius: 8px;
  }

  .form-actions {
    flex-direction: column;
    gap: 1rem;
    margin-top: 2.5rem;
    padding-top: 2rem;
  }

  .btn {
    width: 100%;
    min-height: 48px;
    font-size: 1.1rem;
    padding: 1rem 1.5rem;
    border-radius: 8px;
  }

  .error-message {
    font-size: 1rem;
    margin-top: 0.5rem;
  }

  .error-section .error-message {
    padding: 1.25rem;
    border-radius: 8px;
    font-size: 1rem;
  }

  .error-icon {
    font-size: 1.5rem;
    margin-right: 0.75rem;
  }
}

/* Small mobile styles */
@media (max-width: 480px) {
  .journey-form-container {
    padding: 0.75rem;
  }

  .form-section {
    padding: 1rem;
  }

  .header-section h2 {
    font-size: 1.25rem;
  }

  .stops-header h3,
  .basic-info-section h3 {
    font-size: 1.1rem;
  }

  .stop-item {
    padding: 1rem;
  }

  .stop-order {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .form-control {
    font-size: 1rem;
    padding: 0.875rem;
  }

  .btn {
    font-size: 1rem;
    padding: 0.875rem 1.25rem;
    min-height: 44px;
  }

  .stop-actions .btn {
    min-width: 40px;
    min-height: 40px;
    padding: 0.625rem;
    font-size: 1rem;
  }
}