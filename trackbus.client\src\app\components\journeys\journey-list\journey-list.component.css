.journey-list-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.header-section h2 {
  color: #1976d2;
  margin: 0;
}

.search-section {
  margin-bottom: 2rem;
}

.search-box {
  position: relative;
  max-width: 400px;
}

.search-input {
  width: 100%;
  padding: 0.75rem 2.5rem 0.75rem 1rem;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s;
}

.search-input:focus {
  outline: none;
  border-color: #1976d2;
}

.search-icon {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
}

.table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.journeys-table {
  width: 100%;
  border-collapse: collapse;
}

.journeys-table th {
  background-color: #f5f5f5;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #ddd;
}

.journeys-table th.sortable {
  cursor: pointer;
  user-select: none;
  transition: background-color 0.3s;
}

.journeys-table th.sortable:hover {
  background-color: #e8e8e8;
}

.sort-indicator {
  margin-left: 0.5rem;
  opacity: 0.5;
}

.sort-indicator.active {
  opacity: 1;
  color: #1976d2;
}

.journeys-table td {
  padding: 1rem;
  border-bottom: 1px solid #eee;
  vertical-align: top;
}

.journeys-table tr:hover {
  background-color: #f9f9f9;
}

.code-cell {
  font-weight: 600;
  color: #1976d2;
}

.description-cell {
  max-width: 250px;
}

.stops-cell {
  text-align: center;
}

.stops-count {
  background-color: #e3f2fd;
  color: #1976d2;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.route-cell {
  max-width: 300px;
}

.route-preview {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.route-start,
.route-end {
  background-color: #1976d2;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.route-arrow {
  color: #666;
  font-weight: bold;
}

.route-via {
  color: #666;
  font-size: 0.75rem;
  font-style: italic;
}

.no-route {
  color: #999;
  font-style: italic;
  font-size: 0.875rem;
}

.actions-cell {
  white-space: nowrap;
}

.btn {
  display: inline-block;
  padding: 0.5rem 1rem;
  margin: 0 0.25rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
}

.btn-primary {
  background-color: #1976d2;
  color: white;
}

.btn-primary:hover {
  background-color: #1565c0;
}

.btn-info {
  background-color: #17a2b8;
  color: white;
}

.btn-info:hover {
  background-color: #138496;
}

.btn-warning {
  background-color: #ffc107;
  color: #212529;
}

.btn-warning:hover {
  background-color: #e0a800;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover {
  background-color: #c82333;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #5a6268;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.no-data {
  text-align: center;
  padding: 4rem 2rem;
  color: #666;
}

.no-data-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.no-data h3 {
  color: #333;
  margin-bottom: 1rem;
}

.loading-section {
  text-align: center;
  padding: 4rem 2rem;
}

.loading-spinner {
  display: inline-block;
}

.spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1976d2;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-section {
  text-align: center;
  padding: 2rem;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #f5c6cb;
}

.error-icon {
  font-size: 1.5rem;
  margin-right: 0.5rem;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modal-content h3 {
  margin-top: 0;
  color: #333;
}

.warning-text {
  color: #dc3545;
  font-size: 0.875rem;
  margin-bottom: 1.5rem;
}

.modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.icon {
  margin-right: 0.5rem;
}

/* Enhanced Responsive Design */

/* Tablet styles */
@media (max-width: 1024px) {
  .journey-list-container {
    padding: 1.5rem;
  }

  .journeys-table {
    font-size: 0.9rem;
  }

  .description-cell {
    max-width: 200px;
  }

  .route-cell {
    max-width: 250px;
  }
}

/* Mobile styles - Card layout for better UX */
@media (max-width: 768px) {
  .journey-list-container {
    padding: 1rem;
  }

  .header-section {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .header-section h2 {
    text-align: center;
    font-size: 1.5rem;
  }

  .search-box {
    max-width: none;
  }

  .search-input {
    padding: 1rem;
    font-size: 1.1rem;
    border-radius: 12px;
    min-height: 44px;
  }

  .table-container {
    border-radius: 12px;
  }

  /* Hide table headers on mobile */
  .journeys-table thead {
    display: none;
  }

  .journeys-table,
  .journeys-table tbody,
  .journeys-table tr,
  .journeys-table td {
    display: block;
  }

  .journeys-table tr {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    margin-bottom: 1rem;
    padding: 1.25rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  .journeys-table tr:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
  }

  .journeys-table td {
    padding: 0.5rem 0;
    border: none;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .journeys-table td:before {
    content: attr(data-label);
    font-weight: 600;
    color: #666;
    flex-shrink: 0;
    width: 80px;
    margin-right: 1rem;
  }

  .code-cell {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
  }

  .description-cell {
    max-width: none;
    overflow: visible;
    text-overflow: initial;
    white-space: normal;
    margin-bottom: 0.5rem;
  }

  .route-cell {
    max-width: none;
    margin-bottom: 0.5rem;
  }

  .route-preview {
    flex-direction: row;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  .actions-cell {
    display: flex;
    flex-direction: row;
    gap: 0.5rem;
    justify-content: flex-end;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
  }

  .actions-cell:before {
    display: none;
  }

  .btn-sm {
    min-width: 44px;
    min-height: 44px;
    padding: 0.75rem;
    font-size: 1rem;
    border-radius: 8px;
  }

  .modal-content {
    margin: 1rem;
    border-radius: 12px;
    padding: 1.5rem;
  }

  .modal-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .modal-actions .btn {
    min-height: 48px;
    font-size: 1.1rem;
    border-radius: 8px;
  }

  .no-data {
    padding: 3rem 1.5rem;
  }

  .no-data-icon {
    font-size: 3rem;
  }

  .no-data h3 {
    font-size: 1.25rem;
  }
}

/* Small mobile styles */
@media (max-width: 480px) {
  .journey-list-container {
    padding: 0.75rem;
  }

  .header-section h2 {
    font-size: 1.25rem;
  }

  .journeys-table tr {
    padding: 1rem;
    margin-bottom: 0.75rem;
  }

  .journeys-table td:before {
    width: 70px;
    font-size: 0.875rem;
  }

  .code-cell {
    font-size: 1rem;
  }

  .btn-sm {
    min-width: 40px;
    min-height: 40px;
    padding: 0.625rem;
    font-size: 0.875rem;
  }

  .actions-cell {
    gap: 0.375rem;
  }

  .modal-content {
    margin: 0.75rem;
    padding: 1.25rem;
  }
}